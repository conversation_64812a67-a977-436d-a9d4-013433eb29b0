package com.geeksec.nta.alarm.infrastructure.repository;

import com.geeksec.nta.alarm.domain.aggregate.alarm.Alarm;
import com.geeksec.nta.alarm.domain.valueobject.AlarmStatusEnum;
import com.geeksec.nta.alarm.domain.repository.AlarmRepository;
import com.geeksec.nta.alarm.domain.valueobject.AlarmId;
import com.geeksec.nta.alarm.domain.valueobject.ThreatLevel;
import com.geeksec.nta.alarm.infrastructure.mapper.AlarmMapper;
import com.geeksec.services.common.infrastructure.persistence.BaseRepository;
import com.geeksec.services.common.shared.dto.PageResultVo;
import com.mybatisflex.core.query.QueryWrapper;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 告警仓储实现
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class AlarmRepositoryImpl extends BaseRepository<Alarm, String, AlarmMapper> implements AlarmRepository {

    private final AlarmMapper alarmMapper;

    @Override
    protected AlarmMapper getMapper() {
        return alarmMapper;
    }

    @Override
    protected String getIdFromAggregate(Alarm aggregate) {
        return (String) aggregate.getId();
    }
    
    // save 方法已由 BaseRepository 提供
    
    @Override
    public Optional<Alarm> findById(AlarmId alarmId) {
        return super.findById(alarmId.getValue());
    }

    @Override
    public Optional<Alarm> findByIndexAndId(String index, String alarmId) {
        log.debug("根据索引和ID查找告警: index={}, id={}", index, alarmId);

        try {
            // 这里需要根据具体的查询需求实现
            // 可能需要调用ES或其他数据源
            Alarm alarm = alarmMapper.selectOneById(alarmId);
            if (alarm != null && index.equals(alarm.getIndex())) {
                return Optional.of(alarm);
            }
            return Optional.empty();
        } catch (Exception e) {
            log.error("根据索引和ID查找告警失败: index={}, id={}", index, alarmId, e);
            return Optional.empty();
        }
    }

    @Override
    public boolean deleteById(AlarmId alarmId) {
        try {
            super.deleteById(alarmId.getValue());
            return true;
        } catch (Exception e) {
            log.error("删除告警失败: {}", alarmId.getValue(), e);
            return false;
        }
    }
    
    @Override
    public int batchDelete(List<AlarmId> alarmIds) {
        log.debug("批量删除告警: {}", alarmIds.size());

        try {
            List<String> ids = alarmIds.stream()
                    .map(AlarmId::getValue)
                    .collect(Collectors.toList());

            int deleted = alarmMapper.deleteBatchByIds(ids);
            log.info("批量删除告警成功: 删除数量={}", deleted);
            return deleted;
        } catch (Exception e) {
            log.error("批量删除告警失败", e);
            throw new RuntimeException("批量删除告警失败", e);
        }
    }

    @Override
    public PageResultVo<Alarm> findByPage(AlarmQuery query) {
        log.debug("分页查询告警");

        try {
            // 构建查询条件
            // 由于AlarmQuery是接口，这里需要根据具体实现来构建查询
            // 暂时返回空结果
            return PageResultVo.of(List.of(), 0L, 1, 20);
        } catch (Exception e) {
            log.error("分页查询告警失败", e);
            throw new RuntimeException("分页查询告警失败", e);
        }
    }

    @Override
    public long count(AlarmQuery query) {
        log.debug("统计告警数量");

        try {
            // 构建查询条件
            // 由于AlarmQuery是接口，这里需要根据具体实现来统计
            return alarmMapper.selectCountByQuery(null);
        } catch (Exception e) {
            log.error("统计告警数量失败", e);
            return 0;
        }
    }

    @Override
    public boolean existsById(AlarmId alarmId) {
        return super.existsById(alarmId.getValue());
    }

    // deleteAll 方法已由 BaseRepository 提供

    /**
     * 删除所有告警并返回删除数量
     */
    public long deleteAllAndCount() {
        log.warn("删除所有告警");
        try {
            long count = super.count();
            super.deleteAll();
            log.warn("删除所有告警成功: {} 个", count);
            return count;
        } catch (Exception e) {
            log.error("删除所有告警失败", e);
            throw new RuntimeException("删除所有告警失败", e);
        }
    }

    @Override
    public List<Alarm> findByStatus(AlarmStatusEnum status) {
        log.debug("根据状态查找告警: {}", status);
        try {
            QueryWrapper queryWrapper = QueryWrapper.create()
                    .eq("status", status.getCode());
            return getMapper().selectListByQuery(queryWrapper);
        } catch (Exception e) {
            log.error("根据状态查找告警失败: {}", status, e);
            throw new RuntimeException("根据状态查找告警失败", e);
        }
    }

    @Override
    public List<Alarm> findByThreatLevel(ThreatLevel threatLevel) {
        log.debug("根据威胁等级查找告警: {}", threatLevel);
        try {
            QueryWrapper queryWrapper = QueryWrapper.create()
                    .eq("attack_level", threatLevel.getLevel());
            return getMapper().selectListByQuery(queryWrapper);
        } catch (Exception e) {
            log.error("根据威胁等级查找告警失败: {}", threatLevel, e);
            throw new RuntimeException("根据威胁等级查找告警失败", e);
        }
    }

    @Override
    public List<Alarm> findByAlarmType(String alarmType) {
        log.debug("根据告警类型查找告警: {}", alarmType);
        try {
            QueryWrapper queryWrapper = QueryWrapper.create()
                    .eq("alarm_type", alarmType);
            return getMapper().selectListByQuery(queryWrapper);
        } catch (Exception e) {
            log.error("根据告警类型查找告警失败: {}", alarmType, e);
            throw new RuntimeException("根据告警类型查找告警失败", e);
        }
    }

    @Override
    public List<Alarm> findByTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        log.debug("根据时间范围查找告警: {} - {}", startTime, endTime);
        try {
            QueryWrapper queryWrapper = QueryWrapper.create()
                    .ge("alarm_time", startTime)
                    .le("alarm_time", endTime);
            return getMapper().selectListByQuery(queryWrapper);
        } catch (Exception e) {
            log.error("根据时间范围查找告警失败: {} - {}", startTime, endTime, e);
            throw new RuntimeException("根据时间范围查找告警失败", e);
        }
    }

    @Override
    public List<Alarm> findByAttackerIp(String attackerIp) {
        log.debug("根据攻击者IP查找告警: {}", attackerIp);
        try {
            // TODO: 实现根据攻击者IP查找告警的逻辑
            return List.of();
        } catch (Exception e) {
            log.error("根据攻击者IP查找告警失败: {}", attackerIp, e);
            throw new RuntimeException("根据攻击者IP查找告警失败", e);
        }
    }

    @Override
    public List<Alarm> findByVictimIp(String victimIp) {
        log.debug("根据受害者IP查找告警: {}", victimIp);
        try {
            // TODO: 实现根据受害者IP查找告警的逻辑
            return List.of();
        } catch (Exception e) {
            log.error("根据受害者IP查找告警失败: {}", victimIp, e);
            throw new RuntimeException("根据受害者IP查找告警失败", e);
        }
    }

    @Override
    public List<Alarm> findHighThreatUnprocessedAlarms() {
        log.debug("查找高威胁等级的未处理告警");
        try {
            // TODO: 实现查找高威胁等级的未处理告警的逻辑
            return List.of();
        } catch (Exception e) {
            log.error("查找高威胁等级的未处理告警失败", e);
            throw new RuntimeException("查找高威胁等级的未处理告警失败", e);
        }
    }

    @Override
    public List<Alarm> findRelatedAlarms(Alarm alarm, int timeRangeHours) {
        log.debug("查找相关告警: {}, 时间范围: {}小时", alarm.getAlarmId().getValue(), timeRangeHours);
        try {
            // TODO: 实现查找相关告警的逻辑
            return List.of();
        } catch (Exception e) {
            log.error("查找相关告警失败: {}", alarm.getAlarmId().getValue(), e);
            throw new RuntimeException("查找相关告警失败", e);
        }
    }

    @Override
    public int batchSave(List<Alarm> alarms) {
        log.debug("批量保存告警: 数量={}", alarms.size());
        try {
            // TODO: 实现批量保存告警的逻辑
            return 0;
        } catch (Exception e) {
            log.error("批量保存告警失败", e);
            throw new RuntimeException("批量保存告警失败", e);
        }
    }

    @Override
    public int batchUpdateStatus(List<AlarmId> alarmIds, AlarmStatusEnum status, Integer updatedBy) {
        log.debug("批量更新告警状态: 数量={}, 状态={}", alarmIds.size(), status);
        try {
            // TODO: 实现批量更新告警状态的逻辑
            return 0;
        } catch (Exception e) {
            log.error("批量更新告警状态失败", e);
            throw new RuntimeException("批量更新告警状态失败", e);
        }
    }
}
