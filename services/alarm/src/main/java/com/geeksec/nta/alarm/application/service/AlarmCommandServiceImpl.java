package com.geeksec.nta.alarm.application.service;

import java.util.List;
import java.util.Optional;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.geeksec.nta.alarm.application.command.DeleteAlarmCommand;
import com.geeksec.nta.alarm.application.command.UpdateAlarmStatusCommand;
import com.geeksec.nta.alarm.domain.aggregate.alarm.Alarm;
import com.geeksec.nta.alarm.domain.repository.AlarmRepository;
import com.geeksec.nta.alarm.domain.valueobject.AlarmId;
import com.geeksec.nta.alarm.domain.valueobject.AlarmStatusEnum;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 告警命令应用服务实现
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AlarmCommandServiceImpl implements AlarmCommandService {

    private final AlarmRepository alarmRepository;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateAlarmStatus(UpdateAlarmStatusCommand command) {
        log.info("更新告警状态: alarmIds={}, status={}, threatLevel={}",
                command.getAlarmIds(), command.getNewStatus(), command.getNewThreatLevel());

        // 验证命令参数
        command.validate();

        try {
            // 处理批量更新
            int successCount = 0;
            for (AlarmId alarmId : command.getAlarmIds()) {
                Optional<Alarm> alarmOpt = alarmRepository.findById(alarmId);
                if (alarmOpt.isEmpty()) {
                    log.warn("告警不存在: {}", alarmId.getValue());
                    continue;
                }

                Alarm alarm = alarmOpt.get();

                // 更新状态（包含状态机验证）
                if (command.getNewStatus() != null) {
                    AlarmStatusEnum newStatus = AlarmStatusEnum.valueOf(command.getNewStatus());
                    Integer operatorId = parseOperatorId(command.getOperator());

                    if (Boolean.TRUE.equals(command.getForceUpdate())) {
                        // 强制更新，跳过状态机验证
                        alarm.setStatus(newStatus.getCode());
                    } else {
                        // 正常更新，使用状态机验证
                        alarm.updateStatus(newStatus, operatorId);
                    }
                }

                // 更新威胁等级（如果指定）
                if (command.getNewThreatLevel() != null) {
                    Integer operatorId = parseOperatorId(command.getOperator());
                    alarm.updateThreatLevel(command.getNewThreatLevel(), operatorId);
                }

                alarmRepository.save(alarm);
                successCount++;
            }

            log.info("更新告警状态成功: 成功数量={}", successCount);
            return successCount > 0;
        } catch (Exception e) {
            log.error("更新告警状态失败: alarmIds={}", command.getAlarmIds(), e);
            throw new RuntimeException("更新告警状态失败", e);
        }
    }

    @Override
    @Transactional
    public boolean deleteAlarm(DeleteAlarmCommand command) {
        log.info("删除告警: alarmId={}", command.getAlarmId().getValue());

        try {
            boolean deleted = alarmRepository.deleteById(command.getAlarmId());

            if (deleted) {
                log.info("删除告警成功: {}", command.getAlarmId().getValue());
            } else {
                log.warn("告警不存在或删除失败: {}", command.getAlarmId().getValue());
            }

            return deleted;
        } catch (Exception e) {
            log.error("删除告警失败: {}", command.getAlarmId().getValue(), e);
            throw new RuntimeException("删除告警失败", e);
        }
    }

    @Override
    @Transactional
    public int batchDeleteAlarms(List<DeleteAlarmCommand> commands) {
        log.info("批量删除告警: 数量={}", commands.size());

        int successCount = 0;
        for (DeleteAlarmCommand command : commands) {
            try {
                if (deleteAlarm(command)) {
                    successCount++;
                }
            } catch (Exception e) {
                log.error("批量删除告警失败: alarmId={}", command.getAlarmId().getValue(), e);
            }
        }

        log.info("批量删除告警完成: 成功={}, 总数={}", successCount, commands.size());
        return successCount;
    }

    @Override
    @Transactional
    public long deleteAllAlarms() {
        log.warn("删除所有告警 - 危险操作");

        try {
            long deletedCount = alarmRepository.deleteAllAndCount();
            log.warn("删除所有告警完成: 删除数量={}", deletedCount);
            return deletedCount;
        } catch (Exception e) {
            log.error("删除所有告警失败", e);
            throw new RuntimeException("删除所有告警失败", e);
        }
    }

    @Override
    @Transactional
    public long archiveExpiredAlarms(int daysToKeep) {
        log.info("归档过期告警: 保留天数={}", daysToKeep);

        try {
            // TODO: 实现归档逻辑
            log.info("归档过期告警完成");
            return 0;
        } catch (Exception e) {
            log.error("归档过期告警失败", e);
            throw new RuntimeException("归档过期告警失败", e);
        }
    }

    // ==================== 私有方法 ====================

    /**
     * 解析操作者ID
     *
     * @param operator 操作者字符串
     * @return 操作者ID
     */
    private Integer parseOperatorId(String operator) {
        if (operator == null || operator.trim().isEmpty()) {
            return null;
        }

        try {
            return Integer.parseInt(operator.trim());
        } catch (NumberFormatException e) {
            // 如果不是数字，返回默认值或抛出异常
            log.warn("无法解析操作者ID: {}", operator);
            return 0; // 默认系统操作者
        }
    }

}
