package com.geeksec.task.repository.impl;

import com.geeksec.services.common.infrastructure.persistence.BaseEntityRepository;
import com.geeksec.task.mapper.TaskMapper;
import com.geeksec.task.model.entity.Task;
import com.geeksec.task.repository.TaskRepository;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 任务仓储实现类
 * 
 * 基于 BaseEntityRepository 和 MyBatis-Flex 的任务仓储实现
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
@Repository
public class TaskRepositoryImpl extends BaseEntityRepository<Task, Long, TaskMapper> 
        implements TaskRepository {
    
    private final TaskMapper taskMapper;
    
    public TaskRepositoryImpl(TaskMapper taskMapper) {
        this.taskMapper = taskMapper;
    }
    
    @Override
    protected TaskMapper getMapper() {
        return taskMapper;
    }
    
    @Override
    protected Long getIdFromEntity(Task entity) {
        return entity.getId();
    }
    
    // ==================== TaskRepository 接口方法实现 ====================
    
    @Override
    public List<Task> findByUserId(Integer userId) {
        log.debug("根据用户ID查询任务列表: {}", userId);
        try {
            QueryWrapper queryWrapper = QueryWrapper.create()
                    .eq("user_id", userId)
                    .orderBy("create_time", false); // 按创建时间倒序
            return getMapper().selectListByQuery(queryWrapper);
        } catch (Exception e) {
            log.error("根据用户ID查询任务列表失败: {}", userId, e);
            throw new RuntimeException("查询任务列表失败", e);
        }
    }
    
    @Override
    public List<Task> findByTaskStatus(String taskStatus) {
        log.debug("根据任务状态查询任务列表: {}", taskStatus);
        try {
            QueryWrapper queryWrapper = QueryWrapper.create()
                    .eq("task_status", taskStatus)
                    .orderBy("create_time", false);
            return getMapper().selectListByQuery(queryWrapper);
        } catch (Exception e) {
            log.error("根据任务状态查询任务列表失败: {}", taskStatus, e);
            throw new RuntimeException("查询任务列表失败", e);
        }
    }
    
    @Override
    public List<Task> findByTaskType(String taskType) {
        log.debug("根据任务类型查询任务列表: {}", taskType);
        try {
            QueryWrapper queryWrapper = QueryWrapper.create()
                    .eq("task_type", taskType)
                    .orderBy("create_time", false);
            return getMapper().selectListByQuery(queryWrapper);
        } catch (Exception e) {
            log.error("根据任务类型查询任务列表失败: {}", taskType, e);
            throw new RuntimeException("查询任务列表失败", e);
        }
    }
    
    @Override
    public Task findLatestByUserId(Integer userId) {
        log.debug("查询用户的最新任务: {}", userId);
        try {
            QueryWrapper queryWrapper = QueryWrapper.create()
                    .eq("user_id", userId)
                    .orderBy("create_time", false)
                    .limit(1);
            List<Task> tasks = getMapper().selectListByQuery(queryWrapper);
            return tasks.isEmpty() ? null : tasks.get(0);
        } catch (Exception e) {
            log.error("查询用户最新任务失败: {}", userId, e);
            throw new RuntimeException("查询最新任务失败", e);
        }
    }
    
    @Override
    public long countByUserId(Integer userId) {
        log.debug("统计用户的任务数量: {}", userId);
        try {
            QueryWrapper queryWrapper = QueryWrapper.create()
                    .eq("user_id", userId);
            return getMapper().selectCountByQuery(queryWrapper);
        } catch (Exception e) {
            log.error("统计用户任务数量失败: {}", userId, e);
            throw new RuntimeException("统计任务数量失败", e);
        }
    }
    
    @Override
    public long countByUserIdAndStatus(Integer userId, String taskStatus) {
        log.debug("统计用户指定状态的任务数量: userId={}, status={}", userId, taskStatus);
        try {
            QueryWrapper queryWrapper = QueryWrapper.create()
                    .eq("user_id", userId)
                    .eq("task_status", taskStatus);
            return getMapper().selectCountByQuery(queryWrapper);
        } catch (Exception e) {
            log.error("统计用户指定状态任务数量失败: userId={}, status={}", userId, taskStatus, e);
            throw new RuntimeException("统计任务数量失败", e);
        }
    }
    
    @Override
    public List<Task> findRunningTasks() {
        log.debug("查询正在运行的任务");
        try {
            QueryWrapper queryWrapper = QueryWrapper.create()
                    .eq("task_status", "RUNNING")
                    .orderBy("create_time", false);
            return getMapper().selectListByQuery(queryWrapper);
        } catch (Exception e) {
            log.error("查询正在运行的任务失败", e);
            throw new RuntimeException("查询运行中任务失败", e);
        }
    }
    
    @Override
    public List<Task> findPendingTasks() {
        log.debug("查询待处理的任务");
        try {
            QueryWrapper queryWrapper = QueryWrapper.create()
                    .eq("task_status", "PENDING")
                    .orderBy("create_time", true); // 按创建时间正序，先进先出
            return getMapper().selectListByQuery(queryWrapper);
        } catch (Exception e) {
            log.error("查询待处理任务失败", e);
            throw new RuntimeException("查询待处理任务失败", e);
        }
    }
    
    @Override
    public boolean updateTaskStatus(Integer taskId, String newStatus) {
        log.debug("更新任务状态: taskId={}, newStatus={}", taskId, newStatus);
        try {
            Task task = getMapper().selectOneById(taskId.longValue());
            if (task != null) {
                task.setTaskStatus(newStatus);
                task.setUpdatedAt(java.time.LocalDateTime.now());
                getMapper().update(task);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("更新任务状态失败: taskId={}, newStatus={}", taskId, newStatus, e);
            throw new RuntimeException("更新任务状态失败", e);
        }
    }
    
    @Override
    public boolean updateTaskProgress(Integer taskId, Integer progress) {
        log.debug("更新任务进度: taskId={}, progress={}", taskId, progress);
        try {
            Task task = getMapper().selectOneById(taskId.longValue());
            if (task != null) {
                task.setProgress(progress);
                task.setUpdatedAt(java.time.LocalDateTime.now());
                getMapper().update(task);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("更新任务进度失败: taskId={}, progress={}", taskId, progress, e);
            throw new RuntimeException("更新任务进度失败", e);
        }
    }
    
    @Override
    public void deleteByUserId(Integer userId) {
        log.debug("删除用户的所有任务: {}", userId);
        try {
            QueryWrapper queryWrapper = QueryWrapper.create()
                    .eq("user_id", userId);
            getMapper().deleteByQuery(queryWrapper);
        } catch (Exception e) {
            log.error("删除用户任务失败: {}", userId, e);
            throw new RuntimeException("删除用户任务失败", e);
        }
    }
    
    @Override
    public void deleteCompletedTasks() {
        log.debug("删除已完成的任务");
        try {
            QueryWrapper queryWrapper = QueryWrapper.create()
                    .eq("task_status", "COMPLETED");
            getMapper().deleteByQuery(queryWrapper);
        } catch (Exception e) {
            log.error("删除已完成任务失败", e);
            throw new RuntimeException("删除已完成任务失败", e);
        }
    }
    
    // ==================== IService 接口方法（继承自父类） ====================
    // 基础的 CRUD 操作已由 BaseEntityRepository 提供
    // 这里只需要实现特定的业务方法
}
